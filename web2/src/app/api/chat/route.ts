import { NextRequest, NextResponse } from 'next/server';
import { type Message, LangChainAdapter } from 'ai';
import { designToHtmlGraph } from "../../../graph/designToCode"
import { graph, getSimpleChatGraph } from "../../../graph/simpleChat"

import { convertVercelMessageToLangChainMessage } from '@/utils/message-converters';
import { logToolCallsInDevelopment } from '@/utils/stream-logging';
import { extractOrCreateThreadId, buildGraphConfig, validateSession } from '@/utils/session-manager';

/**
 * This handler initializes and calls an tool calling ReAct agent.
 * See the docs for more information:
 *
 * https://langchain-ai.github.io/langgraphjs/tutorials/quickstart/
 */
/**
 * 错误响应生成器
 */
function createErrorResponse(error: any, context: string) {
  const status = error?.status || error?.statusCode || 500;
  const message = error?.message || '未知错误';

  console.error(`Chat API: ${context} 错误`, {
    status,
    message,
    stack: error?.stack
  });

  // 根据错误类型返回不同的状态码和消息
  if (status === 502 || status === 503 || status === 504) {
    return NextResponse.json(
      {
        error: '服务暂时不可用，请稍后再试',
        type: 'SERVICE_UNAVAILABLE',
        retryAfter: 30
      },
      { status: 503 }
    );
  }

  if (status === 429) {
    return NextResponse.json(
      {
        error: '请求过于频繁，请稍后再试',
        type: 'RATE_LIMIT',
        retryAfter: 60
      },
      { status: 429 }
    );
  }

  if (status >= 400 && status < 500) {
    return NextResponse.json(
      {
        error: '请求格式有误，请检查输入内容',
        type: 'CLIENT_ERROR'
      },
      { status: 400 }
    );
  }

  return NextResponse.json(
    {
      error: '服务器内部错误，请稍后再试',
      type: 'SERVER_ERROR'
    },
    { status: 500 }
  );
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    console.log('Chat API: 收到请求', {
      messagesCount: body.messages?.length || 0,
      hasMessages: !!body.messages,
      modelConfig: body.modelConfig,
      hasThreadId: !!body.thread_id
    });

    // 提取或创建thread_id
    const thread_id = extractOrCreateThreadId(body, 'chat');
    console.log('Chat API: 使用会话ID', thread_id);

    /**
     * We represent intermediate steps as system messages for display purposes,
     * but don't want them in the chat history.
     */
    const messages = (body.messages ?? [])
      .filter((message: Message) => message.role === 'user' || message.role === 'assistant')
      .map(convertVercelMessageToLangChainMessage);

    console.log('Chat API: 处理后的消息', {
      messagesCount: messages.length,
      lastMessage: messages[messages.length - 1]?.content
    });

    // 获取最后一条用户消息作为输入
    const lastUserMessage = messages
      .filter((msg: any) => msg.getType() === 'human')
      .pop();

    const userInput = (lastUserMessage?.content as string) || '';

    // 输入验证
    if (!userInput.trim()) {
      return NextResponse.json(
        {
          error: '请输入有效的消息内容',
          type: 'INVALID_INPUT'
        },
        { status: 400 }
      );
    }

    /**
     * Stream back all generated tokens and steps from their runs.
     *
     * streamMode: "messages"
     * See: https://langchain-ai.github.io/langgraphjs/how-tos/stream-tokens/
     */
    const graphInput = {
      messages: messages,
      input: userInput,
      output: '',
      modelConfig: body.modelConfig || { type: 'google' }
    };

    // 构建graph配置，包含thread_id用于持久化
    const graphConfig = buildGraphConfig(thread_id);

    console.log('Chat API: 调用 graph', {
      input: userInput,
      messagesCount: messages.length,
      thread_id: thread_id
    });

    try {
      // 获取带持久化支持的graph实例
      const persistentGraph = await getSimpleChatGraph();

      // 使用 streamEvents 方法，包含thread_id配置
      const eventStream = persistentGraph.streamEvents(graphInput, {
        version: 'v2',
        ...graphConfig
      });

      // 暂时禁用流式日志处理器来测试基本功能
      const transformedStream = logToolCallsInDevelopment(eventStream);

      // Adapt the LangChain stream to Vercel AI SDK Stream
      return LangChainAdapter.toDataStreamResponse(transformedStream);
    } catch (graphError) {
      console.error('Chat API: Graph 执行错误', graphError);
      return createErrorResponse(graphError, 'Graph 执行');
    }
  } catch (e: any) {
    console.error('Chat API: 请求处理错误', e);
    return createErrorResponse(e, '请求处理');
  }
}
