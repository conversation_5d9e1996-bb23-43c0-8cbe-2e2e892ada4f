import { NextRequest, NextResponse } from 'next/server';
import { 
  createSession, 
  validateSession, 
  getSessionHistory, 
  deleteSession,
  cleanupExpiredSessions 
} from '@/utils/session-manager';
import { checkMongoHealth } from '@/config/mongodb';

/**
 * 会话管理API
 * GET: 获取会话信息或列表
 * POST: 创建新会话
 * DELETE: 删除会话
 */

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const thread_id = searchParams.get('thread_id');
    const action = searchParams.get('action');

    // 健康检查
    if (action === 'health') {
      const isHealthy = await checkMongoHealth();
      return NextResponse.json({ 
        status: isHealthy ? 'healthy' : 'unhealthy',
        mongodb: isHealthy 
      });
    }

    // 清理过期会话
    if (action === 'cleanup') {
      const maxAgeHours = parseInt(searchParams.get('maxAge') || '24');
      const cleanedCount = await cleanupExpiredSessions(maxAgeHours);
      return NextResponse.json({ 
        message: `清理了 ${cleanedCount} 个过期会话`,
        cleanedCount 
      });
    }

    // 获取特定会话信息
    if (thread_id) {
      const isValid = await validateSession(thread_id);
      if (!isValid) {
        return NextResponse.json(
          { error: '会话不存在' },
          { status: 404 }
        );
      }

      const limit = parseInt(searchParams.get('limit') || '10');
      const history = await getSessionHistory(thread_id, limit);
      
      return NextResponse.json({
        thread_id,
        valid: true,
        history,
        historyCount: history.length
      });
    }

    // 如果没有指定thread_id，返回基本信息
    return NextResponse.json({
      message: '会话管理API',
      endpoints: {
        'GET ?thread_id=xxx': '获取会话信息',
        'GET ?action=health': '健康检查',
        'GET ?action=cleanup': '清理过期会话',
        'POST': '创建新会话',
        'DELETE ?thread_id=xxx': '删除会话'
      }
    });

  } catch (error) {
    console.error('Sessions API GET: 错误', error);
    return NextResponse.json(
      { error: '获取会话信息失败' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const sessionType = body.sessionType || 'chat';

    if (!['chat', 'coding'].includes(sessionType)) {
      return NextResponse.json(
        { error: '无效的会话类型，必须是 chat 或 coding' },
        { status: 400 }
      );
    }

    const session = createSession(sessionType as 'chat' | 'coding');
    
    return NextResponse.json({
      message: '会话创建成功',
      thread_id: session.thread_id,
      sessionType,
      created_at: new Date().toISOString()
    });

  } catch (error) {
    console.error('Sessions API POST: 错误', error);
    return NextResponse.json(
      { error: '创建会话失败' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const thread_id = searchParams.get('thread_id');

    if (!thread_id) {
      return NextResponse.json(
        { error: '缺少 thread_id 参数' },
        { status: 400 }
      );
    }

    // 验证会话是否存在
    const isValid = await validateSession(thread_id);
    if (!isValid) {
      return NextResponse.json(
        { error: '会话不存在' },
        { status: 404 }
      );
    }

    // 删除会话
    const success = await deleteSession(thread_id);
    
    if (success) {
      return NextResponse.json({
        message: '会话删除成功',
        thread_id,
        deleted_at: new Date().toISOString()
      });
    } else {
      return NextResponse.json(
        { error: '删除会话失败' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Sessions API DELETE: 错误', error);
    return NextResponse.json(
      { error: '删除会话失败' },
      { status: 500 }
    );
  }
}
