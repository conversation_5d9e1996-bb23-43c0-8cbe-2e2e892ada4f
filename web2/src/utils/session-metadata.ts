import { getMongoClient } from '@/config/mongodb';
import { SessionInfo, updateSessionInfo } from './session-manager';

// 会话标题生成配置
interface TitleGenerationConfig {
  maxLength: number;
  fallbackTemplate: string;
  keywords: string[];
}

// 性能指标接口
interface PerformanceMetrics {
  response_time: number;
  token_count?: number;
  error_count: number;
  success_rate: number;
}

// 用户行为分析接口
interface UserBehavior {
  session_duration: number;
  message_frequency: number;
  preferred_topics: string[];
  interaction_patterns: {
    peak_hours: number[];
    avg_session_length: number;
    retry_rate: number;
  };
}

/**
 * 基于消息内容自动生成会话标题
 */
export async function generateSessionTitle(
  firstMessage: string,
  sessionType: 'chat' | 'coding' = 'chat',
  config: Partial<TitleGenerationConfig> = {}
): Promise<string> {
  const defaultConfig: TitleGenerationConfig = {
    maxLength: 50,
    fallbackTemplate: sessionType === 'chat' ? '聊天会话' : '编程会话',
    keywords: sessionType === 'chat' 
      ? ['问题', '帮助', '咨询', '讨论', '学习']
      : ['代码', '编程', '开发', '调试', '实现']
  };

  const finalConfig = { ...defaultConfig, ...config };

  try {
    // 清理和截取消息内容
    const cleanMessage = firstMessage
      .replace(/[^\w\s\u4e00-\u9fff]/g, '') // 移除特殊字符，保留中英文
      .trim()
      .substring(0, 100);

    if (!cleanMessage) {
      return `${finalConfig.fallbackTemplate} - ${new Date().toLocaleString()}`;
    }

    // 简单的关键词提取和标题生成
    let title = '';
    
    // 检查是否包含关键词
    const foundKeywords = finalConfig.keywords.filter(keyword => 
      cleanMessage.toLowerCase().includes(keyword.toLowerCase())
    );

    if (foundKeywords.length > 0) {
      // 基于关键词生成标题
      const keyword = foundKeywords[0];
      const messageStart = cleanMessage.substring(0, 30);
      title = `${keyword}：${messageStart}`;
    } else {
      // 使用消息开头作为标题
      title = cleanMessage.substring(0, 40);
    }

    // 确保标题长度不超过限制
    if (title.length > finalConfig.maxLength) {
      title = title.substring(0, finalConfig.maxLength - 3) + '...';
    }

    return title || `${finalConfig.fallbackTemplate} - ${new Date().toLocaleString()}`;
  } catch (error) {
    console.error('生成会话标题失败:', error);
    return `${finalConfig.fallbackTemplate} - ${new Date().toLocaleString()}`;
  }
}

/**
 * 提取消息中的标签
 */
export function extractTagsFromMessage(message: string, sessionType: 'chat' | 'coding'): string[] {
  const tags: string[] = [];
  
  // 预定义的标签映射
  const tagMappings = {
    chat: {
      '问题': ['问题', '疑问', '请问', '如何', '怎么'],
      '学习': ['学习', '教学', '解释', '说明'],
      '讨论': ['讨论', '交流', '分享', '观点'],
      '帮助': ['帮助', '协助', '支持', '解决']
    },
    coding: {
      'JavaScript': ['javascript', 'js', 'node', 'react', 'vue'],
      'Python': ['python', 'py', 'django', 'flask'],
      'TypeScript': ['typescript', 'ts'],
      '调试': ['调试', 'debug', '错误', 'error', 'bug'],
      '优化': ['优化', '性能', 'performance', '改进'],
      'API': ['api', '接口', 'rest', 'graphql'],
      '数据库': ['数据库', 'database', 'sql', 'mongodb', 'mysql']
    }
  };

  const mappings = tagMappings[sessionType];
  const lowerMessage = message.toLowerCase();

  // 检查每个标签类别
  Object.entries(mappings).forEach(([tag, keywords]) => {
    if (keywords.some(keyword => lowerMessage.includes(keyword.toLowerCase()))) {
      tags.push(tag);
    }
  });

  // 限制标签数量
  return tags.slice(0, 5);
}

/**
 * 记录性能指标
 */
export async function recordPerformanceMetrics(
  threadId: string,
  metrics: PerformanceMetrics
): Promise<void> {
  try {
    const client = await getMongoClient();
    const db = client.db();
    const collection = db.collection('session_metrics');

    await collection.insertOne({
      thread_id: threadId,
      timestamp: new Date(),
      ...metrics
    });

    // 更新会话的性能统计
    await updateSessionPerformanceStats(threadId, metrics);
  } catch (error) {
    console.error('记录性能指标失败:', error);
  }
}

/**
 * 更新会话的性能统计
 */
async function updateSessionPerformanceStats(
  threadId: string,
  metrics: PerformanceMetrics
): Promise<void> {
  try {
    const client = await getMongoClient();
    const db = client.db();
    const metricsCollection = db.collection('session_metrics');

    // 计算平均响应时间
    const avgMetrics = await metricsCollection.aggregate([
      { $match: { thread_id: threadId } },
      {
        $group: {
          _id: null,
          avg_response_time: { $avg: '$response_time' },
          total_tokens: { $sum: '$token_count' },
          total_errors: { $sum: '$error_count' },
          total_requests: { $sum: 1 }
        }
      }
    ]).toArray();

    if (avgMetrics.length > 0) {
      const stats = avgMetrics[0];
      const successRate = ((stats.total_requests - stats.total_errors) / stats.total_requests) * 100;

      await updateSessionInfo(threadId, {
        metadata: {
          performance: {
            avg_response_time: Math.round(stats.avg_response_time),
            total_duration: stats.total_requests * stats.avg_response_time,
            success_rate: Math.round(successRate * 100) / 100,
            total_tokens: stats.total_tokens || 0
          }
        }
      });
    }
  } catch (error) {
    console.error('更新会话性能统计失败:', error);
  }
}

/**
 * 分析用户行为模式
 */
export async function analyzeUserBehavior(userId: string): Promise<UserBehavior | null> {
  try {
    const client = await getMongoClient();
    const db = client.db();
    const sessionsCollection = db.collection('sessions');
    const metricsCollection = db.collection('session_metrics');

    // 获取用户的所有会话
    const userSessions = await sessionsCollection.find({ user_id: userId }).toArray();
    
    if (userSessions.length === 0) {
      return null;
    }

    // 计算会话持续时间
    const sessionDurations = userSessions.map(session => {
      const start = new Date(session.created_at);
      const end = new Date(session.last_accessed);
      return end.getTime() - start.getTime();
    });

    const avgSessionDuration = sessionDurations.reduce((a, b) => a + b, 0) / sessionDurations.length;

    // 计算消息频率
    const totalMessages = userSessions.reduce((sum, session) => sum + (session.message_count || 0), 0);
    const messageFrequency = totalMessages / userSessions.length;

    // 分析偏好主题（基于标签）
    const allTags = userSessions.flatMap(session => session.tags || []);
    const tagCounts = allTags.reduce((acc, tag) => {
      acc[tag] = (acc[tag] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const preferredTopics = Object.entries(tagCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([tag]) => tag);

    // 分析交互模式
    const creationHours = userSessions.map(session => 
      new Date(session.created_at).getHours()
    );

    const hourCounts = creationHours.reduce((acc, hour) => {
      acc[hour] = (acc[hour] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    const peakHours = Object.entries(hourCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3)
      .map(([hour]) => parseInt(hour));

    // 计算重试率（基于错误指标）
    const totalErrors = await metricsCollection.aggregate([
      { $match: { thread_id: { $in: userSessions.map(s => s.thread_id) } } },
      { $group: { _id: null, total_errors: { $sum: '$error_count' }, total_requests: { $sum: 1 } } }
    ]).toArray();

    const retryRate = totalErrors.length > 0 
      ? (totalErrors[0].total_errors / totalErrors[0].total_requests) * 100 
      : 0;

    return {
      session_duration: Math.round(avgSessionDuration / 1000 / 60), // 转换为分钟
      message_frequency: Math.round(messageFrequency * 100) / 100,
      preferred_topics: preferredTopics,
      interaction_patterns: {
        peak_hours: peakHours,
        avg_session_length: Math.round(avgSessionDuration / 1000 / 60),
        retry_rate: Math.round(retryRate * 100) / 100
      }
    };
  } catch (error) {
    console.error('分析用户行为失败:', error);
    return null;
  }
}

/**
 * 自动更新会话元数据
 */
export async function updateSessionMetadata(
  threadId: string,
  messageContent: string,
  messageRole: 'user' | 'assistant',
  responseTime?: number
): Promise<void> {
  try {
    // 获取当前会话信息
    const client = await getMongoClient();
    const db = client.db();
    const collection = db.collection('sessions');
    const session = await collection.findOne({ thread_id: threadId });

    if (!session) {
      return;
    }

    const updates: any = {
      last_accessed: new Date(),
      message_count: (session.message_count || 0) + 1
    };

    // 如果是第一条用户消息，生成标题和标签
    if (messageRole === 'user' && session.message_count === 0) {
      const title = await generateSessionTitle(messageContent, session.session_type);
      const tags = extractTagsFromMessage(messageContent, session.session_type);
      
      updates.title = title;
      updates.tags = [...(session.tags || []), ...tags].slice(0, 10); // 限制标签数量
    }

    // 更新最后一条消息
    updates.last_message = {
      content: messageContent.substring(0, 200), // 限制长度
      role: messageRole,
      timestamp: new Date()
    };

    await updateSessionInfo(threadId, updates);

    // 记录性能指标
    if (responseTime && messageRole === 'assistant') {
      await recordPerformanceMetrics(threadId, {
        response_time: responseTime,
        error_count: 0,
        success_rate: 100
      });
    }
  } catch (error) {
    console.error('更新会话元数据失败:', error);
  }
}
