import { v4 as uuidv4 } from 'uuid';
import { getMongoCheckpointer } from '@/config/mongodb';

export interface SessionConfig {
  thread_id: string;
  checkpoint_ns?: string;
}

export interface SessionInfo {
  thread_id: string;
  created_at: Date;
  last_accessed: Date;
  message_count: number;
  session_type: 'chat' | 'coding';
}

/**
 * 创建新的会话
 */
export function createSession(sessionType: 'chat' | 'coding' = 'chat'): SessionConfig {
  const thread_id = uuidv4();
  
  console.log(`SessionManager: 创建新会话 ${thread_id} (类型: ${sessionType})`);
  
  return {
    thread_id,
    checkpoint_ns: '' // 默认命名空间
  };
}

/**
 * 获取会话配置
 */
export function getSessionConfig(thread_id: string, checkpoint_ns: string = ''): SessionConfig {
  return {
    thread_id,
    checkpoint_ns
  };
}

/**
 * 验证会话是否存在
 */
export async function validateSession(thread_id: string): Promise<boolean> {
  try {
    const checkpointer = await getMongoCheckpointer();
    const config = { configurable: { thread_id } };
    
    // 尝试获取会话状态
    const checkpoint = await checkpointer.getTuple(config);
    return checkpoint !== undefined;
  } catch (error) {
    console.error(`SessionManager: 验证会话 ${thread_id} 失败`, error);
    return false;
  }
}

/**
 * 获取会话历史信息
 */
export async function getSessionHistory(thread_id: string, limit: number = 10) {
  try {
    const checkpointer = await getMongoCheckpointer();
    const config = { configurable: { thread_id } };
    
    // 获取会话历史
    const history = [];
    let count = 0;
    
    for await (const checkpoint of checkpointer.list(config)) {
      if (count >= limit) break;
      
      history.push({
        checkpoint_id: checkpoint.config.configurable?.checkpoint_id,
        timestamp: checkpoint.checkpoint?.ts,
        step: checkpoint.metadata?.step,
        source: checkpoint.metadata?.source
        // 移除 writes 属性，因为 CheckpointMetadata 类型中没有这个属性
      });
      
      count++;
    }
    
    return history;
  } catch (error) {
    console.error(`SessionManager: 获取会话历史 ${thread_id} 失败`, error);
    return [];
  }
}

/**
 * 删除会话
 */
export async function deleteSession(thread_id: string): Promise<boolean> {
  try {
    const checkpointer = await getMongoCheckpointer();
    await checkpointer.deleteThread(thread_id);
    
    console.log(`SessionManager: 会话 ${thread_id} 已删除`);
    return true;
  } catch (error) {
    console.error(`SessionManager: 删除会话 ${thread_id} 失败`, error);
    return false;
  }
}

/**
 * 清理过期会话 (可选功能)
 */
export async function cleanupExpiredSessions(maxAgeHours: number = 24): Promise<number> {
  try {
    const checkpointer = await getMongoCheckpointer();
    const cutoffTime = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000);
    
    // 这里需要直接访问MongoDB集合来实现清理逻辑
    // 因为MongoDBSaver可能没有直接的批量清理方法
    console.log(`SessionManager: 开始清理 ${maxAgeHours} 小时前的会话`);
    
    // 实际实现需要根据MongoDBSaver的具体API调整
    return 0; // 返回清理的会话数量
  } catch (error) {
    console.error('SessionManager: 清理过期会话失败', error);
    return 0;
  }
}

/**
 * 从请求中提取或创建thread_id
 */
export function extractOrCreateThreadId(
  body: any, 
  sessionType: 'chat' | 'coding' = 'chat'
): string {
  // 优先使用请求中的thread_id
  if (body.thread_id && typeof body.thread_id === 'string') {
    console.log(`SessionManager: 使用现有会话 ${body.thread_id}`);
    return body.thread_id;
  }
  
  // 如果没有提供thread_id，创建新会话
  const session = createSession(sessionType);
  console.log(`SessionManager: 创建新会话 ${session.thread_id}`);
  return session.thread_id;
}

/**
 * 构建LangGraph配置对象
 */
export function buildGraphConfig(thread_id: string, checkpoint_ns: string = ''): any {
  return {
    configurable: {
      thread_id,
      checkpoint_ns
    }
  };
}
